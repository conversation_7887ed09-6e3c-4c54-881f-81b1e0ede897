import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:chewie/chewie.dart';
import 'package:video_player/video_player.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/media_selection_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';

class MediaPreview extends StatefulWidget {
  const MediaPreview({super.key});

  @override
  State<MediaPreview> createState() => _MediaPreviewState();
}

class _MediaPreviewState extends State<MediaPreview> {
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;

  @override
  void dispose() {
    _chewieController?.dispose();
    _videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MediaSelectionCubit, MediaSelectionState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () => _handleTap(context, state),
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
              ),
            ),
            child: _buildMediaContent(state),
          ),
        );
      },
    );
  }

  void _handleTap(BuildContext context, MediaSelectionState state) {
    switch (state.mediaType) {
      case PostMediaType.image:
        context.read<MediaSelectionCubit>().pickImage();
        break;
      case PostMediaType.video:
        context.read<MediaSelectionCubit>().pickVideo();
        break;
      case PostMediaType.links:
        // Do nothing for links, they're handled by action buttons
        break;
    }
  }

  Widget _buildMediaContent(MediaSelectionState state) {
    switch (state.mediaType) {
      case PostMediaType.image:
        return _buildImagePreview(state);
      case PostMediaType.video:
        return _buildVideoPreview(state);
      case PostMediaType.links:
        return _buildLinksPreview(state);
    }
  }

  Widget _buildImagePreview(MediaSelectionState state) {
    if (state.imageFile != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: kIsWeb
            ? Image.memory(
                state.webImageBytes!,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
              )
            : Image.file(
                File(state.imageFile!.path!),
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
              ),
      );
    }
    return _buildPlaceholder(Icons.image, 'Tap to add image');
  }

  Widget _buildVideoPreview(MediaSelectionState state) {
    if (state.videoFile != null) {
      _initializeVideoPlayer(state);
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: _chewieController != null
            ? Chewie(controller: _chewieController!)
            : const Center(child: CircularProgressIndicator()),
      );
    }
    return _buildPlaceholder(Icons.videocam, 'Tap to add video');
  }

  Widget _buildLinksPreview(MediaSelectionState state) {
    if (state.links.isNotEmpty) {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.link,
              size: 32,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 4),
            Text(
              '${state.links.length} link${state.links.length > 1 ? 's' : ''}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }
    return _buildPlaceholder(Icons.link, 'Tap to add links');
  }

  Widget _buildPlaceholder(IconData icon, String text) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 32,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
        ),
        const SizedBox(height: 8),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  void _initializeVideoPlayer(MediaSelectionState state) {
    if (_videoController != null) return;

    if (kIsWeb) {
      // For web, we'll need to handle differently
      // For now, show placeholder
      return;
    } else {
      _videoController = VideoPlayerController.file(
        File(state.videoFile!.path!),
      );
      _videoController!.initialize().then((_) {
        // Check video duration (30 second limit)
        final duration = _videoController!.value.duration;
        if (duration.inSeconds > 30) {
          // Show error or trim video
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Video must be 30 seconds or less')),
            );
            context.read<MediaSelectionCubit>().clearMedia();
          }
          return;
        }

        _chewieController = ChewieController(
          videoPlayerController: _videoController!,
          autoPlay: false,
          looping: false,
          aspectRatio: _videoController!.value.aspectRatio,
          showControls: true,
          showControlsOnInitialize: false,
        );
        setState(() {});
      });
    }
  }
}
