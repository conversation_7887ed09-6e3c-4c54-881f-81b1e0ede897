import 'dart:typed_data';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:file_picker/file_picker.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';

class MediaSelectionState {
  final PostMediaType mediaType;
  final PlatformFile? imageFile;
  final PlatformFile? videoFile;
  final Uint8List? webImageBytes;
  final Uint8List? webVideoBytes;
  final List<String> links;

  MediaSelectionState({
    required this.mediaType,
    this.imageFile,
    this.videoFile,
    this.webImageBytes,
    this.webVideoBytes,
    this.links = const [],
  });

  MediaSelectionState copyWith({
    PostMediaType? mediaType,
    PlatformFile? imageFile,
    PlatformFile? videoFile,
    Uint8List? webImageBytes,
    Uint8List? webVideoBytes,
    List<String>? links,
  }) {
    return MediaSelectionState(
      mediaType: mediaType ?? this.mediaType,
      imageFile: imageFile ?? this.imageFile,
      videoFile: videoFile ?? this.videoFile,
      webImageBytes: webImageBytes ?? this.webImageBytes,
      webVideoBytes: webVideoBytes ?? this.webVideoBytes,
      links: links ?? this.links,
    );
  }
}

class MediaSelectionCubit extends Cubit<MediaSelectionState> {
  MediaSelectionCubit()
    : super(MediaSelectionState(mediaType: PostMediaType.image));

  Future<void> pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        emit(
          state.copyWith(
            mediaType: PostMediaType.image,
            imageFile: file,
            webImageBytes: file.bytes,
            videoFile: null,
            webVideoBytes: null,
            links: [],
          ),
        );
      }
    } catch (e) {
      // Handle error silently or emit error state
    }
  }

  Future<void> pickVideo() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        emit(
          state.copyWith(
            mediaType: PostMediaType.video,
            videoFile: file,
            webVideoBytes: file.bytes,
            imageFile: null,
            webImageBytes: null,
            links: [],
          ),
        );
      }
    } catch (e) {
      // Handle error silently or emit error state
    }
  }

  void addLink(String link) {
    if (link.isNotEmpty && !state.links.contains(link)) {
      final updatedLinks = List<String>.from(state.links)..add(link);
      emit(
        state.copyWith(
          mediaType: PostMediaType.links,
          links: updatedLinks,
          imageFile: null,
          videoFile: null,
          webImageBytes: null,
          webVideoBytes: null,
        ),
      );
    }
  }

  void removeLink(String link) {
    final updatedLinks = List<String>.from(state.links)..remove(link);
    emit(state.copyWith(links: updatedLinks));
  }

  void switchToLinksMode() {
    emit(
      state.copyWith(
        mediaType: PostMediaType.links,
        imageFile: null,
        videoFile: null,
        webImageBytes: null,
        webVideoBytes: null,
      ),
    );
  }

  void clearMedia() {
    emit(MediaSelectionState(mediaType: PostMediaType.image));
  }
}
