/*
* Constrained Scaffold
* This widget is used to display the scaffold with a constrained width
* */
import 'package:flutter/material.dart';

class ConstrainedScaffold extends StatelessWidget {
  final Widget body;
  final PreferredSizeWidget? appBar;

  final Widget? bottomNavigationBar;
  const ConstrainedScaffold({
    super.key,
    required this.body,
    this.appBar,
    this.bottomNavigationBar,
  });
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      width: double.infinity,
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 700),
          child: Container(
            decoration: BoxDecoration(
              border: Border.symmetric(
                vertical: BorderSide(
                  color: Theme.of(context).colorScheme.tertiary.withAlpha(150),
                  width: 1,
                ),
              ),
            ),
            child: Scaffold(
              appBar: appBar,
              bottomNavigationBar: bottomNavigationBar,
              body: body,
            ),
          ),
        ),
      ),
    );
  }
}
