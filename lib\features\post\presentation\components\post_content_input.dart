import 'package:flutter/material.dart';

class PostContentInput extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final int maxLines;
  final int maxLength;

  const PostContentInput({
    super.key,
    required this.controller,
    this.hintText = '',
    this.maxLines = 10,
    this.maxLength = 480,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: TextField(
        controller: controller,
        maxLines: maxLines,
        maxLength: maxLength,
        decoration: InputDecoration(
          hintText: hintText,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Theme.of(context).colorScheme.surface,
          counterText: '', // Hide the default counter
        ),
        style: Theme.of(context).textTheme.bodyMedium,
      ),
    );
  }
}
