import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/common/components/SnackBar.dart';
import 'package:social_app_bloc_flutter/features/auth/domain/entities/app_user.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_states.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/media_selection_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cost_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/media_preview.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/post_content_input.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/post_bottom_bar.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_states.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/cubits/wallet_balance_cubit.dart';
import 'package:social_app_bloc_flutter/responsive/constrained_scaffold.dart';

class UploadPostPage extends StatefulWidget {
  const UploadPostPage({super.key});

  @override
  State<UploadPostPage> createState() => _UploadPostPageState();
}

class _UploadPostPageState extends State<UploadPostPage> {
  final textController = TextEditingController();
  AppUser? currentUser;
  PostCategory selectedCategory = PostCategory.politics;

  @override
  void initState() {
    super.initState();
    getCurrentUser();
  }

  @override
  void dispose() {
    textController.dispose();
    super.dispose();
  }

  void getCurrentUser() async {
    currentUser = context.read<AuthCubit>().currentUser;
  }

  void uploadPost() async {
    final mediaState = context.read<MediaSelectionCubit>().state;

    // Check if content is provided
    if (textController.text.isEmpty) {
      showSnack(context, 'Please provide post content');
      return;
    }

    // Check if media is provided based on media type (only for image and video)
    if (mediaState.mediaType == PostMediaType.image &&
        mediaState.imageFile == null) {
      showSnack(context, 'Please select an image');
      return;
    }

    if (mediaState.mediaType == PostMediaType.video &&
        mediaState.videoFile == null) {
      showSnack(context, 'Please select a video');
      return;
    }

    // For links, we allow empty links (text-only posts)

    // Get current post cost
    final postCost = context.read<PostCostCubit>().state;

    // Check if user has sufficient balance
    final currentBalance = context.read<WalletBalanceCubit>().state;
    if (currentBalance < postCost) {
      showSnack(
        context,
        'Insufficient balance. Please add funds to your wallet.',
      );
      return;
    }

    // Optimistically deduct balance
    context.read<WalletBalanceCubit>().deductOptimistically(postCost);

    // create new post object
    final newPost = Post(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: currentUser!.uid,
      userName: currentUser!.name,
      text: textController.text,
      imageUrl: mediaState.mediaType == PostMediaType.image ? '' : '',
      videoUrl: mediaState.mediaType == PostMediaType.video ? '' : '',
      links: mediaState.mediaType == PostMediaType.links
          ? mediaState.links
          : [],
      mediaType: mediaState.mediaType,
      timestamp: DateTime.now(),
      likes: [],
      comments: [],
      category: selectedCategory,
      postCost: postCost,
    );

    // Handle different media types for upload
    if (mediaState.mediaType == PostMediaType.image) {
      if (kIsWeb) {
        context.read<PostCubit>().createPost(
          newPost,
          imageBytes: mediaState.webImageBytes,
        );
      } else {
        context.read<PostCubit>().createPost(
          newPost,
          imagePath: mediaState.imageFile!.path,
        );
      }
    } else if (mediaState.mediaType == PostMediaType.video) {
      if (kIsWeb) {
        context.read<PostCubit>().createPost(
          newPost,
          videoBytes: mediaState.webVideoBytes,
        );
      } else {
        context.read<PostCubit>().createPost(
          newPost,
          videoPath: mediaState.videoFile!.path,
        );
      }
    } else {
      // Links only - no file upload needed
      context.read<PostCubit>().createPost(newPost);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MediaSelectionCubit(),
      child: BlocConsumer<PostCubit, PostStates>(
        builder: (context, state) {
          if (state is PostsLoading || state is PostsUploading) {
            return ConstrainedScaffold(
              body: const Center(child: CircularProgressIndicator()),
            );
          }

          return ConstrainedScaffold(
            appBar: AppBar(
              title: const Text('Create Post'),
              centerTitle: true,
              backgroundColor: Theme.of(context).colorScheme.surface,
              foregroundColor: Theme.of(context).colorScheme.onSurface,
            ),
            body: Column(
              children: [
                // Category title
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  child: BlocBuilder<ProfileCubit, ProfileStates>(
                    builder: (context, profileState) {
                      if (profileState is ProfileLoaded) {
                        selectedCategory =
                            profileState.profileUser.selectedPostCategory;
                      }
                      return Text(
                        selectedCategory.name.toUpperCase(),
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                        textAlign: TextAlign.center,
                      );
                    },
                  ),
                ),

                // Media preview
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: MediaPreview(),
                ),

                const SizedBox(height: 20),

                // Post content input
                Expanded(
                  child: PostContentInput(
                    controller: textController,
                    maxLines: 8,
                  ),
                ),

                // Bottom bar with actions
                PostBottomBar(
                  textController: textController,
                  onPostPressed: uploadPost,
                ),
              ],
            ),
          );
        },
        listener: (context, state) {
          if (state is PostsLoaded) {
            // Clear form and go back
            textController.clear();
            context.read<MediaSelectionCubit>().clearMedia();
            Navigator.pop(context);
          } else if (state is PostsError) {
            showSnack(context, 'Error: ${state.message}');
          }
        },
      ),
    );
  }
}
