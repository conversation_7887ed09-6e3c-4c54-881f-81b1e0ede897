import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/media_selection_cubit.dart';

class MediaActionButtons extends StatelessWidget {
  const MediaActionButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // First row of buttons
        Row(
          children: [
            _buildActionButton(
              context,
              'Photo',
              Icons.photo,
              Colors.purple,
              () {
                final cubit = context.read<MediaSelectionCubit>();
                cubit.clearMedia(); // Clear current media
                cubit.pickImage();
              },
            ),
            const SizedBox(width: 8),
            _buildActionButton(
              context,
              'Camera',
              Icons.camera_alt,
              Colors.teal,
              () => _showCameraOptions(context),
            ),
            const SizedBox(width: 8),
            _buildActionButton(
              context,
              'Video',
              Icons.videocam,
              Colors.blue,
              () {
                final cubit = context.read<MediaSelectionCubit>();
                cubit.clearMedia(); // Clear current media
                cubit.pickVideo();
              },
            ),
            const SizedBox(width: 8),
            _buildActionButton(
              context,
              'Links',
              Icons.link,
              Colors.lightBlue,
              () {
                final cubit = context.read<MediaSelectionCubit>();
                // Switch to links mode first
                cubit.switchToLinksMode();
                _showLinkDialog(context);
              },
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Second row of buttons (disabled for now)
        Row(
          children: [
            _buildActionButton(
              context,
              'Search Images',
              Icons.search,
              Colors.grey,
              () {},
              enabled: false,
            ),
            const SizedBox(width: 8),
            _buildActionButton(
              context,
              'Emoji',
              Icons.emoji_emotions,
              Colors.pink,
              () {},
              enabled: false,
            ),
            const SizedBox(width: 8),
            _buildActionButton(
              context,
              'Poll',
              Icons.poll,
              Colors.green,
              () {},
              enabled: false,
            ),
            const SizedBox(width: 8),
            _buildActionButton(
              context,
              'More',
              Icons.more_horiz,
              Colors.orange,
              () {},
              enabled: false,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback onTap, {
    bool enabled = true,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: enabled ? onTap : null,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          decoration: BoxDecoration(
            color: enabled ? color : Colors.grey.shade400,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 16, color: Colors.white),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCameraOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Take Photo'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement camera photo capture
                context.read<MediaSelectionCubit>().pickImage();
              },
            ),
            ListTile(
              leading: const Icon(Icons.videocam),
              title: const Text('Record Video'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement camera video recording
                context.read<MediaSelectionCubit>().pickVideo();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showLinkDialog(BuildContext context) {
    final TextEditingController linkController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Link'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: linkController,
              decoration: const InputDecoration(
                hintText: 'Enter URL',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.url,
            ),
            const SizedBox(height: 16),
            BlocBuilder<MediaSelectionCubit, MediaSelectionState>(
              builder: (context, state) {
                if (state.links.isEmpty) return const SizedBox();

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Added Links:'),
                    const SizedBox(height: 8),
                    ...state.links.map(
                      (link) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                link,
                                style: Theme.of(context).textTheme.bodySmall,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete, size: 16),
                              onPressed: () => context
                                  .read<MediaSelectionCubit>()
                                  .removeLink(link),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (linkController.text.isNotEmpty) {
                context.read<MediaSelectionCubit>().addLink(
                  linkController.text,
                );
                linkController.clear();
              }
            },
            child: const Text('Add'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }
}
