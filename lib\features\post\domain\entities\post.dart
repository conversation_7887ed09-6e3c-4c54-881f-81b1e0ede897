import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/comment.dart';

enum PostCategory { news, politics, sex, entertainment, sports, religion }

enum PostMediaType { image, video, links }

class Post {
  final String id;
  final String userId;
  final String userName;
  final String text;
  final String imageUrl;
  final String videoUrl;
  final List<String> links;
  final PostMediaType mediaType;
  final DateTime timestamp;
  final List<String> likes;
  final List<Comment> comments;
  final PostCategory category;
  final double postCost;

  Post({
    required this.id,
    required this.userId,
    required this.userName,
    required this.text,
    required this.imageUrl,
    required this.videoUrl,
    required this.links,
    required this.mediaType,
    required this.timestamp,
    required this.likes,
    required this.comments,
    required this.category,
    required this.postCost,
  });

  Post copyWith({String? imageUrl, String? videoUrl}) {
    return Post(
      id: id,
      userId: userId,
      userName: userName,
      text: text,
      imageUrl: imageUrl ?? this.imageUrl,
      videoUrl: videoUrl ?? this.videoUrl,
      links: links,
      mediaType: mediaType,
      timestamp: timestamp,
      likes: likes,
      comments: comments,
      category: category,
      postCost: postCost,
    );
  }

  // convert post ->  to Json
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'text': text,
      'imageUrl': imageUrl,
      'videoUrl': videoUrl,
      'links': links,
      'mediaType': mediaType.name,
      'timestamp': Timestamp.fromDate(timestamp),
      'likes': likes,
      'comments': comments.map((comment) => comment.toJson()).toList(),
      'category': category.name,
      'postCost': postCost,
    };
  }

  factory Post.fromJson(Map<String, dynamic> json) {
    // Prepare comments
    final List<Comment> comments = (json['comments'] as List<dynamic>? ?? [])
        .map((commentJson) => Comment.fromJson(commentJson))
        .toList();
    return Post(
      id: json['id'],
      userId: json['userId'],
      userName: json['userName'],
      text: json['text'],
      imageUrl: json['imageUrl'] ?? '',
      videoUrl: json['videoUrl'] ?? '',
      links: List<String>.from(json['links'] ?? []),
      mediaType: PostMediaType.values.firstWhere(
        (e) => e.name == json['mediaType'],
        orElse: () => PostMediaType.image,
      ),
      timestamp: (json['timestamp'] as Timestamp).toDate(),
      likes: List<String>.from(json['likes'] ?? []),
      comments: comments,
      category: PostCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => PostCategory.politics,
      ),
      postCost: (json['postCost'] as num?)?.toDouble() ?? 0.05,
    );
  }
}
