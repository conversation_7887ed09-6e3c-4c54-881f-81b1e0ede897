import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cost_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/post_cost_display.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/media_action_buttons.dart';

class PostBottomBar extends StatelessWidget {
  final TextEditingController textController;
  final VoidCallback onPostPressed;

  const PostBottomBar({
    super.key,
    required this.textController,
    required this.onPostPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Media Action Buttons
          const MediaActionButtons(),
          
          const SizedBox(height: 16),
          
          // Character count and cost display
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ValueListenableBuilder<TextEditingValue>(
                valueListenable: textController,
                builder: (context, value, child) {
                  return Text(
                    '${value.text.length}/480 characters',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  );
                },
              ),
              BlocBuilder<PostCostCubit, double>(
                builder: (context, cost) {
                  return PostCostDisplay(cost: cost);
                },
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Put Up button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: onPostPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Put Up',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
