import 'dart:typed_data';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/comment.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/post/domain/repos/post_repo.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_states.dart';
import 'package:social_app_bloc_flutter/features/storage/domain/storage_repo.dart';
import 'package:social_app_bloc_flutter/features/wallet/domain/repos/wallet_repo.dart';

class PostCubit extends Cubit<PostStates> {
  final PostRepo postRepo;
  final StorageRepo storageRepo;
  final WalletRepo walletRepo;

  PostCubit({
    required this.postRepo,
    required this.storageRepo,
    required this.walletRepo,
  }) : super(PostsInitial());

  // create a new post
  Future<void> createPost(
    Post post, {
    String? imagePath,
    String? videoPath,
    Uint8List? imageBytes,
    Uint8List? videoBytes,
  }) async {
    String? imageUrl;
    String? videoUrl;

    try {
      emit(PostsUploading());

      // Handle image upload
      if (post.mediaType == PostMediaType.image) {
        if (imagePath != null) {
          imageUrl = await storageRepo.uploadPostImageMobile(
            imagePath,
            post.id,
          );
        } else if (imageBytes != null) {
          imageUrl = await storageRepo.uploadPostImageWeb(imageBytes, post.id);
        }
      }
      // Handle video upload
      else if (post.mediaType == PostMediaType.video) {
        if (videoPath != null) {
          videoUrl = await storageRepo.uploadPostVideoMobile(
            videoPath,
            post.id,
          );
        } else if (videoBytes != null) {
          videoUrl = await storageRepo.uploadPostVideoWeb(videoBytes, post.id);
        }
      }

      // Update post with media URLs
      post = post.copyWith(
        imageUrl: imageUrl ?? post.imageUrl,
        videoUrl: videoUrl ?? post.videoUrl,
      );

      // deduct post cost from user's wallet
      await walletRepo.deductBalance(
        post.userId,
        post.postCost,
        'Post creation: ${post.text.substring(0, post.text.length > 20 ? 20 : post.text.length)}...',
        'Post Creation',
      );

      // create post in the backend
      await postRepo.createPost(post);

      // refetch all posts
      await fetchAllPosts();
    } catch (e) {
      emit(PostsError("Failed to create post: $e"));
    }
  }

  // fetch all posts
  Future<void> fetchAllPosts() async {
    try {
      emit(PostsLoading());
      final posts = await postRepo.fetchAllPosts();
      emit(PostsLoaded(posts));
    } catch (e) {
      emit(PostsError("Failed to fetch posts: $e"));
    }
  }

  // fetch posts by category
  Future<void> fetchPostsByCategory(PostCategory category) async {
    try {
      emit(PostsLoading());
      final posts = await postRepo.fetchPostsByCategory(category);
      emit(PostsLoaded(posts));
    } catch (e) {
      emit(PostsError("Failed to fetch posts by category: $e"));
    }
  }

  // delete post
  Future<void> deletePost(String postId) async {
    try {
      await postRepo.deletePost(postId);
    } catch (e) {
      emit(PostsError("Failed to delete post: $e"));
    }
  }

  // toggle like post
  Future<void> toggleLikePost(String postId, String userId) async {
    try {
      await postRepo.toggleLikePost(postId, userId);
    } catch (e) {
      emit(PostsError("Failed to like/unlike post: $e"));
    }
  }

  // add comment
  Future<void> addComment(String postId, Comment comment) async {
    try {
      await postRepo.addComment(postId, comment);
      await fetchAllPosts();
    } catch (e) {
      emit(PostsError("Failed to add comment: $e"));
    }
  }

  // delete comment
  Future<void> deleteComment(String postId, String commentId) async {
    try {
      await postRepo.deleteComment(postId, commentId);
      await fetchAllPosts();
    } catch (e) {
      emit(PostsError("Failed to delete comment: $e"));
    }
  }
}
