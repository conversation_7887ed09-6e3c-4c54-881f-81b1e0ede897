import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cost_cubit.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/cubits/wallet_cubit.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/cubits/wallet_states.dart';

class PriceSlider extends StatelessWidget {
  const PriceSlider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WalletCubit, WalletStates>(
      builder: (context, walletState) {
        double minValue = 0.05;
        double maxBalance = minValue;

        if (walletState is WalletLoaded) {
          maxBalance = walletState.wallet.balance > minValue
              ? walletState.wallet.balance
              : minValue;
        }

        return BlocBuilder<PostCostCubit, double>(
          builder: (context, currentCost) {
            // Clamp current cost between min & max to avoid assertion error
            double safeValue = currentCost.clamp(minValue, maxBalance);

            // Calculate divisions safely (avoid 0)
            int divisions = ((maxBalance - minValue) * 20).round().clamp(
              1,
              1000,
            );

            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'Post Cost',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 12,
                    ),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '\$${safeValue.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                color: Theme.of(context).colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),

                  // Slider
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: Theme.of(context).colorScheme.tertiary,
                      inactiveTrackColor: Theme.of(context).colorScheme.outline,
                      thumbColor: Theme.of(context).colorScheme.tertiary,
                      overlayColor: Theme.of(
                        context,
                      ).colorScheme.tertiary.withAlpha(50),
                      valueIndicatorColor: Theme.of(
                        context,
                      ).colorScheme.primary,
                      valueIndicatorTextStyle: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                      trackHeight: 2,
                    ),
                    child: Slider(
                      value: safeValue,
                      min: minValue,
                      max: maxBalance,
                      divisions: divisions,
                      onChanged: (value) {
                        context.read<PostCostCubit>().updateCost(value);
                      },
                    ),
                  ),

                  // Min label
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Min: \$${minValue.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                      // Text(
                      //   'Max: \$${maxBalance.toStringAsFixed(2)}',
                      //   style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      //     color: Theme.of(
                      //       context,
                      //     ).colorScheme.onSurface.withOpacity(0.7),
                      //   ),
                      // ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Balance warning
                  if (walletState is WalletLoaded &&
                      safeValue > walletState.wallet.balance)
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.errorContainer,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning,
                            size: 16,
                            color: Theme.of(
                              context,
                            ).colorScheme.onErrorContainer,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              'Insufficient balance. Current balance: \$${walletState.wallet.balance.toStringAsFixed(2)}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onErrorContainer,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
