import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/post_tile.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_states.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/pages/upload_post_page.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/pages/profile_page.dart';
import 'package:social_app_bloc_flutter/features/search/presentation/cubits/pages/search_page.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/cubits/wallet_cubit.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/pages/wallet_page.dart';
import 'package:social_app_bloc_flutter/features/settings/pages/settings_page.dart';
import 'package:social_app_bloc_flutter/responsive/constrained_scaffold.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_states.dart';
import 'package:social_app_bloc_flutter/features/home/<USER>/components/sidebar.dart';
import 'package:social_app_bloc_flutter/features/home/<USER>/cubits/category_cubit.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => HomePageState();
}

class HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  int _currentTabIndex = 0;
  PostCategory? userSelectedCategory;

  @override
  void initState() {
    super.initState();
    loadData();
  }

  void switchTab(int index) {
    if (index >= 0 && index < 6 && index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  void _onCategoryChanged(PostCategory? category) {
    context.read<CategoryCubit>().selectCategory(category);
    setState(() {
      userSelectedCategory = category;
      _currentTabIndex = category != null ? 0 : 1;
    });
  }

  // load data
  void loadData() {
    context.read<PostCubit>().fetchAllPosts();
    final user = context.read<AuthCubit>().currentUser;
    if (user != null) {
      context.read<ProfileCubit>().fetchUserProfile(user.uid);
      context.read<WalletCubit>().loadWallet(user.uid);
    }

    // Initialize category from profile when available
    _initializeCategoryFromProfile();
  }

  void _initializeCategoryFromProfile() {
    final profileState = context.read<ProfileCubit>().state;
    if (profileState is ProfileLoaded) {
      context.read<CategoryCubit>().initializeFromProfile(
        profileState.profileUser.selectedPostCategory,
      );
      setState(() {
        userSelectedCategory = profileState.profileUser.selectedPostCategory;
      });
    }
  }

  Widget _buildHomeFeed() {
    return BlocListener<ProfileCubit, ProfileStates>(
      listener: (context, profileState) {
        if (profileState is ProfileLoaded) {
          // Update category cubit when profile changes
          context.read<CategoryCubit>().initializeFromProfile(
            profileState.profileUser.selectedPostCategory,
          );
          setState(() {
            userSelectedCategory =
                profileState.profileUser.selectedPostCategory;
          });
        }
      },
      child: BlocBuilder<ProfileCubit, ProfileStates>(
        builder: (context, profileState) {
          if (profileState is ProfileLoaded) {
            userSelectedCategory =
                profileState.profileUser.selectedPostCategory;
          }

          return Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 6,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () => setState(() => _currentTabIndex = 0),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 6),
                          decoration: BoxDecoration(
                            color: _currentTabIndex == 0
                                ? Theme.of(context).colorScheme.outline
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Category',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: _currentTabIndex == 0
                                  ? Theme.of(context).colorScheme.secondary
                                  : Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => setState(() => _currentTabIndex = 1),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 6),
                          decoration: BoxDecoration(
                            color: _currentTabIndex == 1
                                ? Theme.of(context).colorScheme.outline
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'All',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: _currentTabIndex == 1
                                  ? Theme.of(context).colorScheme.secondary
                                  : Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(child: _buildPostsList()),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPostsList() {
    return BlocBuilder<CategoryCubit, PostCategory?>(
      builder: (context, sidebarCategory) {
        return BlocBuilder<PostCubit, PostStates>(
          builder: (context, state) {
            if (state is PostsLoading || state is PostsUploading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state is PostsLoaded) {
              List<Post> postsToShow;
              // Determine which posts to show based on current tab and sidebar selection
              if (_currentTabIndex == 0) {
                // Category tab - use sidebar category or user's selected category
                PostCategory? filterCategory =
                    sidebarCategory ?? userSelectedCategory;

                if (filterCategory == null) {
                  return const Center(
                    child: Text('Select a category in the sidebar'),
                  );
                }

                postsToShow = state.posts
                    .where((post) => post.category == filterCategory)
                    .toList();

                if (postsToShow.isEmpty) {
                  return Center(
                    child: Text('No ${filterCategory.name} posts found...'),
                  );
                }
              } else {
                // All posts tab - show all posts regardless of sidebar selection
                postsToShow = state.posts;
                if (postsToShow.isEmpty) {
                  return const Center(child: Text('No posts found...'));
                }
              }

              return ListView.builder(
                itemCount: postsToShow.length,
                itemBuilder: (context, index) {
                  final post = postsToShow[index];
                  return PostTile(
                    post: post,
                    onDeletePressed: () =>
                        context.read<PostCubit>().deletePost(post.id),
                  );
                },
              );
            }

            if (state is PostsError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(state.message),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () =>
                          context.read<PostCubit>().fetchAllPosts(),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            return const SizedBox();
          },
        );
      },
    );
  }

  Widget _buildCurrentPage() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeFeed(); // Home
      case 1:
        return const WalletPage(); // Wallet
      case 2:
        return const UploadPostPage(); // Create Post
      case 3:
        return const SearchPage(); // Search
      case 4:
        final user = context.read<AuthCubit>().currentUser;
        return ProfilePage(uid: user!.uid); // Profile
      case 5:
        return const SettingsPage(); // Settings
      default:
        return _buildHomeFeed();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      body: Row(
        children: [
          BlocBuilder<CategoryCubit, PostCategory?>(
            builder: (context, selectedCategory) {
              return Sidebar(
                selectedCategory: selectedCategory,
                onCategoryChanged: _onCategoryChanged,
              );
            },
          ),
          Expanded(child: _buildCurrentPage()),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(blurRadius: 20, color: Colors.black.withAlpha(25)),
          ],
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 8),
            child: GNav(
              tabBorderRadius: 20,
              rippleColor: Theme.of(context).colorScheme.tertiary.withAlpha(25),
              hoverColor: Theme.of(context).colorScheme.tertiary.withAlpha(13),
              gap: 4,
              activeColor: Theme.of(
                context,
              ).colorScheme.tertiary.withAlpha(200),
              iconSize: 18,
              textSize: 16,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              duration: const Duration(milliseconds: 400),
              tabBackgroundColor: Theme.of(
                context,
              ).colorScheme.primary.withAlpha(25),
              color: Theme.of(context).colorScheme.primary,
              tabs: const [
                GButton(icon: Icons.home, text: 'Home'),
                GButton(icon: Icons.account_balance_wallet, text: 'Wallet'),
                GButton(icon: Icons.add_circle_outline, text: 'Create'),
                GButton(icon: Icons.search, text: 'Search'),
                GButton(icon: Icons.person, text: 'Profile'),
                GButton(icon: Icons.settings, text: 'Settings'),
              ],
              selectedIndex: _selectedIndex,
              onTabChange: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
            ),
          ),
        ),
      ),
    );
  }
}
